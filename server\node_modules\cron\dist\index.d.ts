import { DateTime } from 'luxon';
import { CronTime } from './time';
export { CronJob } from './job';
export { CronTime } from './time';
export { CronCallback, CronCommand, CronContext, CronJobParams, CronOnCompleteCallback, CronOnCompleteCommand, Ranges, TimeUnit } from './types/cron.types';
export declare const sendAt: (cronTime: string | Date | DateTime) => DateTime;
export declare const timeout: (cronTime: string | Date | DateTime) => number;
export declare const validateCronExpression: typeof CronTime.validateCronExpression;
